cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃力度
        jumpForce: 1500,

        // 最大下落高度，超过此高度视为游戏失败
        maxFallHeight: 500,

        // 最大跳跃次数
        maxJumpTimes: 5,

        // 玩家在X轴的固定位置
        fixedPositionX: -200,

        // 滑行持续时间
        slideDuration: 1.0,

        // 滑行时的碰撞体高度缩放
        slideColliderScale: 0.5,

        // 默认动画名称
        defaultAnimationName: "playeridle",

        // 跳跃动画持续时间（秒）
        jumpAnimationDuration: 0.5,

        // // 音效
        // jumpSound: {
        //     default: null,
        //     type: cc.AudioClip
        // },

        // slideSound: {
        //     default: null,
        //     type: cc.AudioClip
        // }
    },

    onLoad() {
        // 初始化状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;

        // 获取刚体组件
        this.rigidBody = this.getComponent(cc.RigidBody);

        // 获取动画组件
        this.animationComponent = this.getComponent(cc.Animation);

        // 获取碰撞体组件
        this.collider = this.getComponent(cc.BoxCollider);
        this.originalColliderSize = this.collider ? this.collider.size.clone() : null;

        // 强制设置刚体为固定旋转
        this.rigidBody.fixedRotation = true;

  

        // 记录初始位置（用于重置）
        this.initialPosition = cc.v2(this.node.x, this.node.y);

        // 设置动画事件监听
        this.setupAnimationEvents();

        // 绑定碰撞事件
        this.node.on('onBeginContact', this.onBeginContact, this);
        this.node.on('onEndContact', this.onEndContact, this);
    },

    start() {
        // 固定X轴位置
        this.node.x = this.fixedPositionX;

        // 播放默认动画
        this.playDefaultAnimation();
    },

    update(dt) {
        // 始终保持X轴位置不变（这是跑酷游戏的特点）
        this.node.x = this.fixedPositionX;

        // 根据是否在地面上播放相应动画
        if (!this.isSliding && this.animationComponent) {
            if (this.isOnGround) {
                // 在地面上播放默认动画
                let currentAnim = this.animationComponent.currentClip;
                if (!currentAnim || currentAnim.name !== this.defaultAnimationName) {
                    this.playDefaultAnimation();
                }
            } else {
                // 不在地面上播放跳跃动画
                let currentAnim = this.animationComponent.currentClip;
                if (!currentAnim || currentAnim.name !== 'playerjump') {
                    this.animationComponent.play('playerjump');
                    console.log("播放跳跃动画（空中状态）");
                }
            }
        }

        // 添加调试信息
        if (Math.floor(Date.now() / 1000) % 2 === 0) { // 每2秒打印一次
            console.log(`玩家状态 - 在地面: ${this.isOnGround}, 位置: (${this.node.x.toFixed(1)}, ${this.node.y.toFixed(1)}), 速度: (${this.rigidBody.linearVelocity.x.toFixed(1)}, ${this.rigidBody.linearVelocity.y.toFixed(1)})`);
        }

        // 检查是否超过最大下落高度
        if (!this.isGameOver && this.initialPosition.y - this.node.y > this.maxFallHeight) {
            this.gameOver();
        }
    },

    setupAnimationEvents() {
        // 使用定时器方式来处理动画切换，更可靠
        console.log("动画事件设置完成");
    },

    playDefaultAnimation() {
        if (this.animationComponent && this.defaultAnimationName) {
            // 检查是否有默认动画
            let animState = this.animationComponent.getAnimationState(this.defaultAnimationName);
            if (animState) {
                this.animationComponent.play(this.defaultAnimationName);
                console.log("播放默认动画:", this.defaultAnimationName);
            } else {
                console.warn("没有找到默认动画:", this.defaultAnimationName);
            }
        }
    },

    jump() {
        if (this.isGameOver || this.isSliding) {
            return;
        }

        // 跳跃条件：在地面上或者还有剩余跳跃次数
        if (this.isOnGround || this.jumpCount < this.maxJumpTimes) {
            // 直接设置速度
            this.rigidBody.linearVelocity = cc.v2(0, this.jumpForce);

            // 增加跳跃计数
            this.jumpCount++;

            // 标记不在地面上
            this.isOnGround = false;

            // 播放跳跃动画
            if (this.animationComponent) {
                this.animationComponent.play('playerjump');
                console.log("跳跃动画播放（主动跳跃）");
            }

            console.log("玩家执行跳跃");

            // 跳跃成功，可以在这里播放音效
            // if (this.jumpSound) {
            //     cc.audioEngine.playEffect(this.jumpSound, false);
            // }
        }
    },

    slide() {
        if (this.isGameOver || this.isSliding || !this.isOnGround) {
            return;
        }

        console.log("玩家执行滑行");

        // 设置滑行状态
        this.isSliding = true;

        // 播放滑行动画
        if (this.animationComponent) {
            this.animationComponent.play('playerslide');
        }

        // 缩小碰撞体（模拟蹲下）
        if (this.collider && this.originalColliderSize) {
            this.collider.size = cc.size(
                this.originalColliderSize.width,
                this.originalColliderSize.height * this.slideColliderScale
            );
            this.collider.apply();
        }

        // 设置滑行结束定时器（使用滑行持续时间）
        this.scheduleOnce(() => {
            this.endSlide();
        }, this.slideDuration);

        // 播放滑行音效
        // if (this.slideSound) {
        //     cc.audioEngine.playEffect(this.slideSound, false);
        // }
    },

    endSlide() {
        if (!this.isSliding) {
            return;
        }

        console.log("玩家滑行结束");

        // 重置滑行状态
        this.isSliding = false;

        // 恢复碰撞体大小
        if (this.collider && this.originalColliderSize) {
            this.collider.size = this.originalColliderSize.clone();
            this.collider.apply();
        }

        // 播放默认动画
        this.playDefaultAnimation();
    },

    onBeginContact(contact, selfCollider, otherCollider) {
        let isPlatform = otherCollider.node.group === 'Ground';
        console.log(`碰撞开始 - 对象: ${otherCollider.node.name}, 分组: ${otherCollider.node.group}, 是地面: ${isPlatform}`);

        if (isPlatform) {
            // 简化地面检测：只要碰到地面且玩家向下运动或静止，就认为落地
            if (this.rigidBody.linearVelocity.y <= 0) {
                this.isOnGround = true;
                this.jumpCount = 0; // 重置跳跃次数
                console.log("落地成功！");
            }
        }
    },

    onEndContact(contact, selfCollider, otherCollider) {
        // 如果离开地面（通过分组检测）
        let isPlatform = otherCollider.node.group === 'Ground';
        console.log(`碰撞结束 - 对象: ${otherCollider.node.name}, 分组: ${otherCollider.node.group}, 是地面: ${isPlatform}`);

        if (isPlatform) {
            // 立即标记为离开地面，不使用延迟
            this.isOnGround = false;
            console.log("离开地面，开始下落");
        }
    },

    gameOver() {
        this.isGameOver = true;
        console.log("游戏结束");

        // 播放失败音效
        // if (this.fallSound) {
        //     cc.audioEngine.playEffect(this.fallSound, false);
        // }

        // cc.director.pause(); // 游戏暂停
    },

    reset() {
        // 重置位置
        this.node.x = this.initialPosition.x;
        this.node.y = this.initialPosition.y;

        // 重置状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;

        // 重置速度
        if (this.rigidBody) {
            this.rigidBody.linearVelocity = cc.v2(0, 0);
        }

        // 重置碰撞体
        if (this.collider && this.originalColliderSize) {
            this.collider.size = this.originalColliderSize.clone();
            this.collider.apply();
        }

        // 取消所有定时器
        this.unscheduleAllCallbacks();
    },

    onDestroy() {
        // 移除碰撞事件监听
        this.node.off('onBeginContact', this.onBeginContact, this);
        this.node.off('onEndContact', this.onEndContact, this);

        // 取消所有定时器
        this.unscheduleAllCallbacks();
    }
});
